import axios, { AxiosInstance, AxiosRequestConfig, AxiosError, AxiosResponse } from 'axios';
import tokenManager from './tokenManager';
import { setupCsrfProtection } from './csrfProtection';
import { csrfManager } from './csrfManager';
import { isBrowser } from './browserUtils';

// Create base axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_BACKEND_URL || '',
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true, // Enable cookies for CSRF protection
});

// Initialize CSRF manager (only in browser)
if (isBrowser()) {
  csrfManager.init({
    tokenEndpoint: '/api/csrf-token',
    headerName: 'X-CSRF-Token',
    cookieName: 'csrf-token',
    excludePaths: ['/api/csrf-token', '/api/auth/login', '/api/auth/register'],
    retryOnFailure: true,
    maxRetries: 1
  });
}

// Setup CSRF protection
setupCsrfProtection(apiClient);
csrfManager.setupInterceptors(apiClient);

// Request interceptor
apiClient.interceptors.request.use(
  (config) => {
    // Get token using unified token manager
    const token = tokenManager.getAnyToken();
    
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error: AxiosError) => {
    console.error('API request error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error: AxiosError) => {
    // Handle common error scenarios
    if (error.response) {
      // Server responded with non-2xx status
      const status = error.response.status;
      
      // Handle authentication errors
      if (status === 401) {
        console.error('Authentication error:', error.response.data);
        // Clear tokens and redirect on authentication failure
        tokenManager.clearAllTokens();
        if (typeof window !== 'undefined') {
          window.location.href = '/?session=expired';
        }
      }
      
      // Handle server errors
      if (status >= 500) {
        console.error('Server error:', error.response.data);
      }
    } else if (error.request) {
      // Request made but no response received
      console.error('Network error - no response received:', error.request);
    } else {
      // Error in setting up the request
      console.error('Request configuration error:', error.message);
    }
    
    return Promise.reject(error);
  }
);

// API endpoints object
export const apiEndpoints = {  
  auth: {
    // Admin auth endpoints
    adminLoginInit: '/api/auth/admin/login/init',
    adminLoginComplete: '/api/auth/admin/login/complete',
    adminLogin: '/api/auth/admin/login',
    adminSession: '/api/auth/admin/session',
    adminLogout: '/api/auth/admin/logout',
    session: '/api/auth/session',
    logout: '/api/auth/logout',
    
    // User auth endpoints
    userLogin: '/api/auth/user/login',
    deviceChallenge: '/api/auth/user/device-challenge',
  },
  admin: {
    // Admin management
    admins: '/api/admin/admins',
    adminById: (id: string) => `/api/admin/admins/${id}`,
    generatePPK: (id: string) => `/api/admin/admins/${id}/generate-ppk`,
    verifyPPK: (id: string) => `/api/admin/admins/${id}/verify-ppk`,

    // User management
    users: '/api/admin/users',
    usersDetailed: '/api/admin/users/detailed',
    userById: (id: string) => `/api/admin/users/${id}`,
    userDetailed: (id: string) => `/api/admin/users/${id}/detailed`,
    updateUserStatus: (id: string) => `/api/admin/users/${id}/status`,
    resetUserDevice: (id: string) => `/api/admin/users/${id}/reset-device`,
    userActivity: (id: string) => `/api/admin/users/${id}/activity`,

    // User detailed data endpoints
    userChatMessages: (id: string) => `/api/admin/users/${id}/chat-messages`,
    userVoiceRecordings: (id: string) => `/api/admin/users/${id}/voice-recordings`,
    userDeviceDetails: (id: string) => `/api/admin/users/${id}/device-details`,
    userVoiceSettings: (id: string) => `/api/admin/users/${id}/voice-settings`,

    // Voice recordings management
    playVoiceRecording: (recordingId: string) => `/api/admin/voice-recordings/${recordingId}/play`,
    downloadVoiceRecording: (recordingId: string) => `/api/admin/voice-recordings/${recordingId}/download`,
    deleteVoiceRecording: (recordingId: string) => `/api/admin/voice-recordings/${recordingId}`,
  },
  users: {
    list: '/api/users',
    byId: (id: string) => `/api/users/${id}`,
    reset: (id: string) => `/api/users/${id}/reset`,
  },
  voice: {
    // Voice modulation endpoints
    serviceStatus: '/api/voice/service-status',
    testModulation: '/api/voice/test-modulation',
    updateUserProfile: '/api/voice/update-user-profile',
    userSettings: '/api/voice/user-settings',
    profiles: '/api/voice/profiles',
    modulationSettings: '/api/voice/modulation-settings',

    // Voice call endpoints
    startCall: '/api/voice/start-call',
    endCall: '/api/voice/end-call',
    connectCall: '/api/voice/connect-call',
    uploadRecording: '/api/voice/upload-recording',
    calls: '/api/voice/calls',
    recording: (callId: string) => `/api/voice/recording/${callId}`,
  },
  dashboard: {
    stats: '/api/system/health',
  },
  controls: {
    systemStats: '/api/system/stats',
    restart: '/api/system/restart',
    maintenance: '/api/system/maintenance',
    encryption: '/api/system/encryption',
    backup: '/api/system/backup',
  },
  chat: {
    byId: (id: string) => `/api/chat/${id}`,
  },
  calls: {
    byId: (id: string) => `/api/calls/${id}`,
  }
};

export default apiClient;
