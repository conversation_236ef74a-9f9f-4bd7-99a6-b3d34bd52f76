// Main Authentication Controller
// Integrates all authentication mechanisms with proper validation and security controls
import { Router } from 'express';
import Joi from 'joi';
import validate from '../middleware/validate';
import { auth, requireAdmin, superAdminOnly, requireUser } from '../middleware/auth';
import rateLimit from 'express-rate-limit';
import csrfProtection from '../middleware/csrf';

// Import modular auth controllers
import { 
  adminLoginInitiate, 
  adminLoginComplete
} from './auth/admin-auth.controller';

import {
  userLoginController,
  deviceChallengeController
} from './auth/user-auth.controller';

import Admin from '../models/Admin';

import { 
  expressionAuthController
} from './auth/expression-auth.controller';

import { 
  logoutController 
} from './auth/logout.controller';

import { 
  verifySessionController,
  refreshAdminSessionController,
  refreshUserSessionController
} from './auth/session.controller';

// Import additional auth routes
import authSubRouter from './auth';

// Create router
const router = Router();

// ==================== Validation Schemas ====================

// Admin auth schemas
const adminInitLoginSchema = Joi.object({
  username: Joi.string().required().trim().max(50)
});

const adminCompleteLoginSchema = Joi.object({
  username: Joi.string().required().trim().max(50),
  password: Joi.string().required().max(100),
  ppkSignature: Joi.string().optional().max(1024),
  challenge: Joi.string().optional().max(256),
  deviceFingerprint: Joi.string().required().max(500),
  csrfToken: Joi.string().optional().max(64)
});

// User auth schemas
const userLoginSchema = Joi.object({
  username: Joi.string().required().trim().max(50),
  expression: Joi.string().required().max(100),
  deviceFingerprint: Joi.string().required().max(500)
});

// Expression-only auth schema (for mobile calculator)
const expressionAuthSchema = Joi.object({
  expression: Joi.string().required().min(3).max(100),
  deviceFingerprint: Joi.string().optional().max(500),
  bleUUID: Joi.string().optional().trim().max(36)
});

const deviceChallengeSchema = Joi.object({
  username: Joi.string().required().trim().max(50)
});

// ==================== Rate Limiting ====================

// Configure rate limiting for login attempts
const loginRateLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // 10 requests per IP per windowMs
  standardHeaders: true, 
  legacyHeaders: false,
  message: { error: 'Too many login attempts, please try again later' }
});

// Configure stricter rate limiting for sensitive operations
const sensitiveRateLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 5, // 5 requests per IP per windowMs
  standardHeaders: true, 
  legacyHeaders: false,
  message: { error: 'Rate limit exceeded for sensitive operation' }
});

// ==================== Route Configuration ====================

// Admin authentication routes
router.post('/admin/login/init', 
  loginRateLimiter,
  validate(adminInitLoginSchema), 
  adminLoginInitiate
);

router.post('/admin/login/complete',
  loginRateLimiter,
  validate(adminCompleteLoginSchema),
  adminLoginComplete
);

// Simple admin login (for testing and basic auth)
router.post('/admin/login',
  loginRateLimiter,
  validate(Joi.object({
    username: Joi.string().required().trim().max(50),
    password: Joi.string().required().max(100)
  })),
  async (req, res) => {
    try {
      const { username, password } = req.body;
      console.log('Admin login attempt:', { username, passwordLength: password?.length });

      // Find admin
      const admin = await Admin.findOne({
        username,
        isActive: true
      });

      console.log('Admin found:', !!admin);
      if (admin) {
        console.log('Admin details:', {
          username: admin.username,
          isActive: admin.isActive,
          passwordHashLength: admin.password?.length
        });
      }

      if (!admin) {
        return res.status(401).json({
          success: false,
          error: 'Invalid credentials'
        });
      }

      // Verify password
      const bcrypt = require('bcrypt');
      const isValidPassword = await bcrypt.compare(password, admin.password);
      console.log('Password validation result:', isValidPassword);

      if (!isValidPassword) {
        return res.status(401).json({
          success: false,
          error: 'Invalid credentials'
        });
      }

      // Generate JWT token
      const jwt = require('jsonwebtoken');
      const token = jwt.sign(
        {
          id: admin._id,
          username: admin.username,
          role: 'admin'
        },
        process.env.JWT_SECRET || 'fallback-secret',
        { expiresIn: '24h' }
      );

      res.json({
        success: true,
        token,
        admin: {
          id: admin._id,
          username: admin.username,
          email: admin.email,
          role: admin.role
        }
      });

    } catch (error) {
      console.error('Admin login error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }
);

// User authentication routes
router.post('/user/login', 
  loginRateLimiter,
  validate(userLoginSchema), 
  userLoginController
);

// Expression-only authentication (mobile calculator)
router.post('/expression', 
  loginRateLimiter,
  validate(expressionAuthSchema), 
  expressionAuthController
);

router.post('/user/device-challenge', 
  loginRateLimiter,
  validate(deviceChallengeSchema), 
  deviceChallengeController
);

// Session verification and refresh
router.get('/session', 
  auth, 
  verifySessionController
);

router.get('/admin/session', 
  auth, 
  requireAdmin,
  verifySessionController
);

router.post('/admin/refresh', 
  auth, 
  requireAdmin,
  refreshAdminSessionController
);

router.post('/user/refresh', 
  auth, 
  requireUser,
  refreshUserSessionController
);

// Logout
router.post('/logout', 
  auth, 
  csrfProtection,
  logoutController
);

router.post('/admin/logout', 
  auth, 
  requireAdmin,
  csrfProtection,
  logoutController
);

// ==================== CSRF and Session Management ====================

// CSRF token endpoint (no authentication required)
router.get('/csrf-token', (req, res) => {
  try {
    // Generate CSRF token
    const crypto = require('crypto');
    const csrfToken = crypto.randomBytes(32).toString('hex');
    
    // Set as httpOnly cookie for verification
    res.cookie('csrf-token', csrfToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60 * 1000 // 24 hours
    });
    
    res.json({
      success: true,
      token: csrfToken
    });
  } catch (error) {
    console.error('CSRF token generation error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate CSRF token'
    });
  }
});

// CSRF token validation endpoint
router.post('/csrf-validate', (req, res) => {
  try {
    const providedToken = req.headers['x-csrf-token'] || req.body.csrfToken;
    const cookieToken = req.cookies['csrf-token'];
    
    const isValid = providedToken && providedToken === cookieToken;
    
    res.json({
      success: true,
      valid: isValid
    });
  } catch (error) {
    console.error('CSRF validation error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to validate CSRF token'
    });
  }
});

// Use additional auth routes
router.use('/', authSubRouter);

// ==================== Export Router ====================
export default router;