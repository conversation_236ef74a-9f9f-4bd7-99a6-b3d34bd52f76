/**
 * Voice Testing Interface Component
 * Simplified, clean interface for testing voice modulation profiles
 */

import React, { useState, useRef, useEffect } from 'react';
import { AudioProcessor, VOICE_PROFILES, VoiceProfile } from '../utils/audioProcessor';

interface VoiceTestingInterfaceProps {
  selectedProfile: string;
  onProfileChange?: (profile: string) => void;
  userRole?: 'superuser' | 'regular';
}

export const VoiceTestingInterface: React.FC<VoiceTestingInterfaceProps> = ({
  selectedProfile,
  onProfileChange,
  userRole = 'regular'
}) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [processedAudioUrl, setProcessedAudioUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [latency, setLatency] = useState<number>(0);

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioProcessorRef = useRef<AudioProcessor | null>(null);
  const streamRef = useRef<MediaStream | null>(null);

  useEffect(() => {
    // Initialize audio processor
    if (AudioProcessor.isSupported()) {
      audioProcessorRef.current = new AudioProcessor();
      setLatency(audioProcessorRef.current.getLatencyEstimate());
    } else {
      setError('Web Audio API not supported in this browser');
    }

    return () => {
      // Cleanup
      if (audioProcessorRef.current) {
        audioProcessorRef.current.stopProcessing();
      }
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }
    };
  }, []);

  const startRecording = async () => {
    try {
      setError(null);
      
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: false,
          noiseSuppression: false,
          autoGainControl: false
        }
      });
      
      streamRef.current = stream;
      
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });
      
      const chunks: BlobPart[] = [];
      
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunks.push(event.data);
        }
      };
      
      mediaRecorder.onstop = () => {
        const blob = new Blob(chunks, { type: 'audio/webm' });
        setAudioBlob(blob);
        stream.getTracks().forEach(track => track.stop());
      };
      
      mediaRecorderRef.current = mediaRecorder;
      mediaRecorder.start();
      setIsRecording(true);
      
      // Auto-stop after 5 seconds
      setTimeout(() => {
        if (mediaRecorder.state === 'recording') {
          mediaRecorder.stop();
          setIsRecording(false);
        }
      }, 5000);
      
    } catch (err) {
      console.error('Recording failed:', err);
      setError('Failed to access microphone. Please check permissions.');
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };

  const applyModulation = async () => {
    if (!audioBlob || !audioProcessorRef.current) return;

    try {
      setIsProcessing(true);
      setError(null);

      const profile = VOICE_PROFILES[selectedProfile];
      if (!profile) {
        throw new Error('Invalid voice profile selected');
      }

      // For demo purposes, we'll generate a sample instead of processing the recorded audio
      // In a real implementation, you'd convert the blob to an audio stream and process it
      const sampleBlob = await audioProcessorRef.current.generateProfileSample(profile);
      
      // Create URL for playback
      if (processedAudioUrl) {
        URL.revokeObjectURL(processedAudioUrl);
      }
      
      const url = URL.createObjectURL(sampleBlob);
      setProcessedAudioUrl(url);
      
    } catch (err) {
      console.error('Modulation failed:', err);
      setError('Failed to apply voice modulation');
    } finally {
      setIsProcessing(false);
    }
  };

  const getAvailableProfiles = () => {
    return Object.entries(VOICE_PROFILES).filter(([key, profile]) => {
      if (userRole === 'superuser' && key === 'NORMAL') {
        return false; // Superusers can't use NORMAL profile
      }
      return true;
    });
  };

  return (
    <div className="space-y-6">
      {/* Profile Selection */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Voice Profile Selection</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {getAvailableProfiles().map(([key, profile]) => (
            <div
              key={key}
              className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                selectedProfile === key
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => onProfileChange?.(key)}
            >
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-gray-900">
                    {key.replace(/_/g, ' ')}
                  </h4>
                  <p className="text-sm text-gray-600 mt-1">
                    {profile.description}
                  </p>
                </div>
                {selectedProfile === key && (
                  <div className="w-4 h-4 bg-blue-500 rounded-full"></div>
                )}
              </div>
              
              {/* Profile Parameters */}
              <div className="mt-3 text-xs text-gray-500 space-y-1">
                <div>Pitch: {profile.pitch > 0 ? '+' : ''}{profile.pitch}</div>
                <div>Tempo: {profile.tempo}x</div>
                <div>Effects: {[
                  profile.reverb > 0 && 'Reverb',
                  profile.distortion > 0 && 'Distortion',
                  profile.chorus && 'Chorus'
                ].filter(Boolean).join(', ') || 'None'}</div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Voice Testing */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">Voice Testing</h3>
          <div className="text-sm text-gray-500">
            Latency: ~{latency}ms
          </div>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-700 text-sm">{error}</p>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Step 1: Record */}
          <div className="text-center">
            <div className="mb-3">
              <div className="w-16 h-16 mx-auto bg-blue-100 rounded-full flex items-center justify-center mb-2">
                <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                </svg>
              </div>
              <h4 className="font-medium text-gray-900">1. Record Voice</h4>
              <p className="text-sm text-gray-600">Record a 5-second sample</p>
            </div>
            
            <button
              onClick={isRecording ? stopRecording : startRecording}
              disabled={isProcessing}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                isRecording
                  ? 'bg-red-600 text-white hover:bg-red-700'
                  : 'bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50'
              }`}
            >
              {isRecording ? 'Stop Recording' : 'Start Recording'}
            </button>
            
            {audioBlob && !isRecording && (
              <div className="mt-2 text-sm text-green-600">
                ✓ Voice sample recorded
              </div>
            )}
          </div>

          {/* Step 2: Apply Modulation */}
          <div className="text-center">
            <div className="mb-3">
              <div className="w-16 h-16 mx-auto bg-purple-100 rounded-full flex items-center justify-center mb-2">
                <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
              <h4 className="font-medium text-gray-900">2. Apply Modulation</h4>
              <p className="text-sm text-gray-600">Process with {selectedProfile.replace(/_/g, ' ')}</p>
            </div>
            
            <button
              onClick={applyModulation}
              disabled={!audioBlob || isProcessing}
              className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 font-medium"
            >
              {isProcessing ? 'Processing...' : 'Apply Modulation'}
            </button>
          </div>

          {/* Step 3: Listen */}
          <div className="text-center">
            <div className="mb-3">
              <div className="w-16 h-16 mx-auto bg-green-100 rounded-full flex items-center justify-center mb-2">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z" />
                </svg>
              </div>
              <h4 className="font-medium text-gray-900">3. Listen to Result</h4>
              <p className="text-sm text-gray-600">Hear the modulated voice</p>
            </div>
            
            {processedAudioUrl ? (
              <div className="space-y-2">
                <audio controls className="w-full">
                  <source src={processedAudioUrl} type="audio/wav" />
                  Your browser does not support audio playback.
                </audio>
                <div className="text-sm text-green-600">
                  ✓ Voice modulation applied
                </div>
              </div>
            ) : (
              <div className="text-sm text-gray-400">
                Complete steps 1 & 2 first
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Technical Info */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-2">Technical Information</h4>
        <div className="text-sm text-gray-600 space-y-1">
          <div>• Processing: Web Audio API (browser-native)</div>
          <div>• Latency: ~{latency}ms (suitable for real-time calls)</div>
          <div>• Quality: 44.1kHz, 16-bit</div>
          <div>• Effects: Real-time pitch shifting, distortion, reverb</div>
        </div>
      </div>
    </div>
  );
};
