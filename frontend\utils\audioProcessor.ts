/**
 * Web Audio API-based Voice Processing
 * Replaces SoX dependency with browser-native real-time audio processing
 */

export interface VoiceProfile {
  pitch: number;          // Pitch shift in semitones (-12 to +12)
  tempo: number;          // Tempo change (0.5 to 2.0)
  reverb: number;         // Reverb amount (0 to 100)
  distortion: number;     // Distortion level (0 to 100)
  formant: number;        // Formant shift (-1000 to +1000 Hz)
  chorus: boolean;        // Add chorus effect
  normalize: boolean;     // Normalize audio levels
  description?: string;
  userType?: string;
}

export const VOICE_PROFILES: Record<string, VoiceProfile> = {
  SECURE_MALE: {
    pitch: -6,
    tempo: 0.95,
    reverb: 15,
    distortion: 8,
    formant: -200,
    chorus: true,
    normalize: true,
    description: "Deep, masculine voice with security distortion",
    userType: "all"
  },
  SECURE_FEMALE: {
    pitch: 4,
    tempo: 1.05,
    reverb: 12,
    distortion: 5,
    formant: 150,
    chorus: true,
    normalize: true,
    description: "Higher pitch, feminine voice with light distortion",
    userType: "all"
  },
  ROBOTIC: {
    pitch: -3,
    tempo: 0.9,
    reverb: 25,
    distortion: 15,
    formant: -400,
    chorus: false,
    normalize: true,
    description: "Mechanical, robotic voice effect",
    userType: "all"
  },
  DEEP_SECURE: {
    pitch: -8,
    tempo: 0.85,
    reverb: 20,
    distortion: 12,
    formant: -500,
    chorus: true,
    normalize: true,
    description: "Very deep, heavily secured voice",
    userType: "all"
  },
  ANONYMOUS: {
    pitch: 0,
    tempo: 1.0,
    reverb: 30,
    distortion: 20,
    formant: 0,
    chorus: false,
    normalize: true,
    description: "Anonymous voice with heavy distortion",
    userType: "all"
  },
  NORMAL: {
    pitch: 0,
    tempo: 1.0,
    reverb: 0,
    distortion: 0,
    formant: 0,
    chorus: false,
    normalize: true,
    description: "No voice modulation (regular users only)",
    userType: "regular"
  }
};

export class AudioProcessor {
  private audioContext: AudioContext | null = null;
  private sourceNode: MediaStreamAudioSourceNode | null = null;
  private outputNode: MediaStreamAudioDestinationNode | null = null;
  private effectsChain: AudioNode[] = [];
  private isProcessing = false;

  constructor() {
    this.initializeAudioContext();
  }

  private async initializeAudioContext(): Promise<void> {
    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      
      // Resume context if suspended (required by some browsers)
      if (this.audioContext.state === 'suspended') {
        await this.audioContext.resume();
      }
    } catch (error) {
      console.error('Failed to initialize AudioContext:', error);
      throw new Error('Web Audio API not supported');
    }
  }

  /**
   * Apply voice modulation to an audio stream
   */
  async processAudioStream(inputStream: MediaStream, profile: VoiceProfile): Promise<MediaStream> {
    if (!this.audioContext) {
      throw new Error('AudioContext not initialized');
    }

    // Create source from input stream
    this.sourceNode = this.audioContext.createMediaStreamSource(inputStream);
    
    // Create output destination
    this.outputNode = this.audioContext.createMediaStreamDestination();

    // Build effects chain
    await this.buildEffectsChain(profile);

    // Connect the chain
    this.connectEffectsChain();

    this.isProcessing = true;
    return this.outputNode.stream;
  }

  /**
   * Build the audio effects chain based on profile
   */
  private async buildEffectsChain(profile: VoiceProfile): Promise<void> {
    if (!this.audioContext) return;

    this.effectsChain = [];

    // 1. Pitch Shifter (using AudioWorklet for better performance)
    if (profile.pitch !== 0) {
      const pitchShifter = await this.createPitchShifter(profile.pitch);
      this.effectsChain.push(pitchShifter);
    }

    // 2. Distortion
    if (profile.distortion > 0) {
      const distortion = this.createDistortion(profile.distortion);
      this.effectsChain.push(distortion);
    }

    // 3. Reverb
    if (profile.reverb > 0) {
      const reverb = await this.createReverb(profile.reverb);
      this.effectsChain.push(reverb);
    }

    // 4. Formant Filter
    if (profile.formant !== 0) {
      const formantFilter = this.createFormantFilter(profile.formant);
      this.effectsChain.push(formantFilter);
    }

    // 5. Chorus (if enabled)
    if (profile.chorus) {
      const chorus = this.createChorus();
      this.effectsChain.push(chorus);
    }

    // 6. Compressor/Normalizer
    if (profile.normalize) {
      const compressor = this.createCompressor();
      this.effectsChain.push(compressor);
    }
  }

  /**
   * Create pitch shifter using ScriptProcessorNode (fallback for AudioWorklet)
   */
  private async createPitchShifter(semitones: number): Promise<AudioNode> {
    if (!this.audioContext) throw new Error('AudioContext not available');

    // Simple pitch shifting using playback rate (basic implementation)
    const bufferSize = 4096;
    const processor = this.audioContext.createScriptProcessor(bufferSize, 1, 1);
    
    const pitchRatio = Math.pow(2, semitones / 12);
    let phase = 0;
    
    processor.onaudioprocess = (event) => {
      const inputBuffer = event.inputBuffer.getChannelData(0);
      const outputBuffer = event.outputBuffer.getChannelData(0);
      
      for (let i = 0; i < bufferSize; i++) {
        // Simple pitch shifting algorithm
        const index = Math.floor(phase);
        if (index < inputBuffer.length - 1) {
          const fraction = phase - index;
          outputBuffer[i] = inputBuffer[index] * (1 - fraction) + inputBuffer[index + 1] * fraction;
        } else {
          outputBuffer[i] = 0;
        }
        
        phase += pitchRatio;
        if (phase >= inputBuffer.length) {
          phase = 0;
        }
      }
    };

    return processor;
  }

  /**
   * Create distortion effect
   */
  private createDistortion(amount: number): WaveShaperNode {
    if (!this.audioContext) throw new Error('AudioContext not available');

    const distortion = this.audioContext.createWaveShaper();
    const samples = 44100;
    const curve = new Float32Array(samples);
    const deg = Math.PI / 180;
    const distortionAmount = amount * 2; // Scale 0-100 to 0-200

    for (let i = 0; i < samples; i++) {
      const x = (i * 2) / samples - 1;
      curve[i] = ((3 + distortionAmount) * x * 20 * deg) / (Math.PI + distortionAmount * Math.abs(x));
    }

    distortion.curve = curve;
    distortion.oversample = '4x';
    return distortion;
  }

  /**
   * Create reverb effect using convolution
   */
  private async createReverb(amount: number): Promise<ConvolverNode> {
    if (!this.audioContext) throw new Error('AudioContext not available');

    const convolver = this.audioContext.createConvolver();
    
    // Generate impulse response for reverb
    const length = this.audioContext.sampleRate * (amount / 100) * 2; // Scale reverb time
    const impulse = this.audioContext.createBuffer(2, length, this.audioContext.sampleRate);
    
    for (let channel = 0; channel < 2; channel++) {
      const channelData = impulse.getChannelData(channel);
      for (let i = 0; i < length; i++) {
        channelData[i] = (Math.random() * 2 - 1) * Math.pow(1 - i / length, 2);
      }
    }
    
    convolver.buffer = impulse;
    return convolver;
  }

  /**
   * Create formant filter
   */
  private createFormantFilter(shift: number): BiquadFilterNode {
    if (!this.audioContext) throw new Error('AudioContext not available');

    const filter = this.audioContext.createBiquadFilter();
    filter.type = 'peaking';
    filter.frequency.value = 1000 + shift; // Base formant frequency + shift
    filter.Q.value = 1;
    filter.gain.value = shift > 0 ? 6 : -6; // Boost or cut
    
    return filter;
  }

  /**
   * Create chorus effect
   */
  private createChorus(): DelayNode {
    if (!this.audioContext) throw new Error('AudioContext not available');

    const delay = this.audioContext.createDelay();
    delay.delayTime.value = 0.02; // 20ms delay for chorus effect
    
    return delay;
  }

  /**
   * Create compressor for normalization
   */
  private createCompressor(): DynamicsCompressorNode {
    if (!this.audioContext) throw new Error('AudioContext not available');

    const compressor = this.audioContext.createDynamicsCompressor();
    compressor.threshold.value = -24;
    compressor.knee.value = 30;
    compressor.ratio.value = 12;
    compressor.attack.value = 0.003;
    compressor.release.value = 0.25;
    
    return compressor;
  }

  /**
   * Connect all effects in the chain
   */
  private connectEffectsChain(): void {
    if (!this.sourceNode || !this.outputNode || this.effectsChain.length === 0) {
      // Direct connection if no effects
      this.sourceNode?.connect(this.outputNode);
      return;
    }

    // Connect source to first effect
    this.sourceNode.connect(this.effectsChain[0]);

    // Connect effects in chain
    for (let i = 0; i < this.effectsChain.length - 1; i++) {
      this.effectsChain[i].connect(this.effectsChain[i + 1]);
    }

    // Connect last effect to output
    this.effectsChain[this.effectsChain.length - 1].connect(this.outputNode);
  }

  /**
   * Stop processing and cleanup
   */
  stopProcessing(): void {
    if (this.sourceNode) {
      this.sourceNode.disconnect();
      this.sourceNode = null;
    }

    this.effectsChain.forEach(node => {
      node.disconnect();
    });
    this.effectsChain = [];

    if (this.outputNode) {
      this.outputNode.disconnect();
      this.outputNode = null;
    }

    this.isProcessing = false;
  }

  /**
   * Generate a sample audio for profile testing
   */
  async generateProfileSample(profile: VoiceProfile): Promise<Blob> {
    if (!this.audioContext) throw new Error('AudioContext not available');

    const duration = 3; // 3 seconds
    const sampleRate = this.audioContext.sampleRate;
    const buffer = this.audioContext.createBuffer(1, duration * sampleRate, sampleRate);
    const data = buffer.getChannelData(0);

    // Generate a test tone (simulating voice)
    for (let i = 0; i < data.length; i++) {
      const t = i / sampleRate;
      data[i] = Math.sin(2 * Math.PI * 200 * t) * 0.3 + 
                Math.sin(2 * Math.PI * 400 * t) * 0.2 + 
                Math.sin(2 * Math.PI * 800 * t) * 0.1;
    }

    // Apply effects to the buffer (simplified for sample generation)
    // In a real implementation, you'd process this through the effects chain

    // Convert to WAV blob
    return this.bufferToWav(buffer);
  }

  /**
   * Convert AudioBuffer to WAV Blob
   */
  private bufferToWav(buffer: AudioBuffer): Blob {
    const length = buffer.length;
    const arrayBuffer = new ArrayBuffer(44 + length * 2);
    const view = new DataView(arrayBuffer);

    // WAV header
    const writeString = (offset: number, string: string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };

    writeString(0, 'RIFF');
    view.setUint32(4, 36 + length * 2, true);
    writeString(8, 'WAVE');
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, 1, true);
    view.setUint32(24, buffer.sampleRate, true);
    view.setUint32(28, buffer.sampleRate * 2, true);
    view.setUint16(32, 2, true);
    view.setUint16(34, 16, true);
    writeString(36, 'data');
    view.setUint32(40, length * 2, true);

    // Convert float samples to 16-bit PCM
    const data = buffer.getChannelData(0);
    let offset = 44;
    for (let i = 0; i < length; i++) {
      const sample = Math.max(-1, Math.min(1, data[i]));
      view.setInt16(offset, sample * 0x7FFF, true);
      offset += 2;
    }

    return new Blob([arrayBuffer], { type: 'audio/wav' });
  }

  /**
   * Get processing latency estimate
   */
  getLatencyEstimate(): number {
    // Web Audio API typically has 20-50ms latency
    return this.audioContext?.baseLatency ? this.audioContext.baseLatency * 1000 : 30;
  }

  /**
   * Check if Web Audio API is supported
   */
  static isSupported(): boolean {
    return !!(window.AudioContext || (window as any).webkitAudioContext);
  }
}
