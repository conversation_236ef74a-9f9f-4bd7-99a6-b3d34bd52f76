import express from 'express';
import multer from 'multer';
import { VoiceCallSimulation } from '../services/voiceCallSimulation';
import { VOICE_PROFILES } from '../services/voiceModulation';
import { authenticateToken } from '../middleware/auth';

const router = express.Router();
const upload = multer({ storage: multer.memoryStorage() });

// Global simulation instance
let callSimulation: VoiceCallSimulation | null = null;

/**
 * Start a voice call simulation
 */
router.post('/start', authenticateToken, async (req, res) => {
  try {
    const { participants } = req.body;

    if (!participants || !Array.isArray(participants)) {
      return res.status(400).json({
        success: false,
        error: 'Participants array is required'
      });
    }

    // Validate participants
    for (const participant of participants) {
      if (!participant.userId || !participant.username || !participant.voiceProfile) {
        return res.status(400).json({
          success: false,
          error: 'Each participant must have userId, username, and voiceProfile'
        });
      }

      if (!VOICE_PROFILES[participant.voiceProfile]) {
        return res.status(400).json({
          success: false,
          error: `Invalid voice profile: ${participant.voiceProfile}`
        });
      }
    }

    // End existing call if any
    if (callSimulation) {
      callSimulation.endCall();
    }

    // Start new simulation
    callSimulation = new VoiceCallSimulation();
    const callId = await callSimulation.startCall(participants.map(p => ({
      ...p,
      voiceProfile: VOICE_PROFILES[p.voiceProfile]
    })));

    // Set up event listeners for real-time updates
    callSimulation.on('audioProcessed', (data) => {
      console.log('Audio processed:', data);
    });

    callSimulation.on('metricsUpdate', (data) => {
      console.log('Metrics update:', data);
    });

    res.json({
      success: true,
      data: {
        callId,
        participants,
        message: 'Voice call simulation started'
      }
    });

  } catch (error) {
    console.error('Failed to start call simulation:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to start call simulation'
    });
  }
});

/**
 * Process audio chunk through the simulation
 */
router.post('/process-audio', authenticateToken, upload.single('audio'), async (req, res) => {
  try {
    if (!callSimulation) {
      return res.status(400).json({
        success: false,
        error: 'No active call simulation'
      });
    }

    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'Audio file is required'
      });
    }

    const { fromUserId } = req.body;
    if (!fromUserId) {
      return res.status(400).json({
        success: false,
        error: 'fromUserId is required'
      });
    }

    const result = await callSimulation.processAudioChunk(req.file.buffer, fromUserId);

    // Return processed audio as blob
    res.setHeader('Content-Type', 'audio/wav');
    res.setHeader('X-Processing-Time', result.metrics.processingTime.toString());
    res.setHeader('X-Total-Latency', result.metrics.totalLatency.toString());
    res.setHeader('X-Audio-Quality', result.metrics.audioQuality.toString());
    
    res.send(result.processedAudio);

  } catch (error) {
    console.error('Failed to process audio:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to process audio'
    });
  }
});

/**
 * Get current call metrics
 */
router.get('/metrics', authenticateToken, (req, res) => {
  try {
    if (!callSimulation) {
      return res.status(400).json({
        success: false,
        error: 'No active call simulation'
      });
    }

    const metrics = callSimulation.getCallMetrics();
    const analysis = callSimulation.getLatencyAnalysis();

    res.json({
      success: true,
      data: {
        metrics,
        analysis
      }
    });

  } catch (error) {
    console.error('Failed to get metrics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get call metrics'
    });
  }
});

/**
 * Simulate different network conditions
 */
router.post('/network-condition', authenticateToken, (req, res) => {
  try {
    if (!callSimulation) {
      return res.status(400).json({
        success: false,
        error: 'No active call simulation'
      });
    }

    const { condition } = req.body;
    const validConditions = ['excellent', 'good', 'poor', 'unstable'];
    
    if (!validConditions.includes(condition)) {
      return res.status(400).json({
        success: false,
        error: `Invalid condition. Must be one of: ${validConditions.join(', ')}`
      });
    }

    callSimulation.simulateNetworkCondition(condition);

    res.json({
      success: true,
      data: {
        condition,
        message: `Network condition set to ${condition}`
      }
    });

  } catch (error) {
    console.error('Failed to set network condition:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to set network condition'
    });
  }
});

/**
 * End the current call simulation
 */
router.post('/end', authenticateToken, (req, res) => {
  try {
    if (!callSimulation) {
      return res.status(400).json({
        success: false,
        error: 'No active call simulation'
      });
    }

    callSimulation.endCall();
    callSimulation = null;

    res.json({
      success: true,
      data: {
        message: 'Call simulation ended'
      }
    });

  } catch (error) {
    console.error('Failed to end call simulation:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to end call simulation'
    });
  }
});

/**
 * Get available voice profiles for simulation
 */
router.get('/profiles', authenticateToken, (req, res) => {
  try {
    const profiles = Object.keys(VOICE_PROFILES).map(name => ({
      name,
      displayName: name.replace(/_/g, ' '),
      ...VOICE_PROFILES[name]
    }));

    res.json({
      success: true,
      data: profiles
    });

  } catch (error) {
    console.error('Failed to get profiles:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get voice profiles'
    });
  }
});

export default router;
