import { EventEmitter } from 'events';
import { VoiceModulationService } from './voiceModulation';
import { VoiceModulationProfile } from '../types/voice';

interface CallParticipant {
  userId: string;
  username: string;
  voiceProfile: VoiceModulationProfile;
  isConnected: boolean;
  latency: number;
}

interface CallMetrics {
  totalLatency: number;
  processingTime: number;
  networkLatency: number;
  audioQuality: number;
  packetsLost: number;
}

export class VoiceCallSimulation extends EventEmitter {
  private voiceModulation: VoiceModulationService;
  private activeCall: {
    callId: string;
    participants: CallParticipant[];
    startTime: Date;
    metrics: CallMetrics;
  } | null = null;

  constructor() {
    super();
    this.voiceModulation = new VoiceModulationService();
  }

  /**
   * Start a simulated voice call
   */
  async startCall(participants: { userId: string; username: string; voiceProfile: VoiceModulationProfile }[]): Promise<string> {
    const callId = `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    this.activeCall = {
      callId,
      participants: participants.map(p => ({
        ...p,
        isConnected: true,
        latency: this.simulateNetworkLatency()
      })),
      startTime: new Date(),
      metrics: {
        totalLatency: 0,
        processingTime: 0,
        networkLatency: 0,
        audioQuality: 95, // Start with high quality
        packetsLost: 0
      }
    };

    this.emit('callStarted', { callId, participants: this.activeCall.participants });
    
    // Start simulating real-time metrics
    this.startMetricsSimulation();
    
    return callId;
  }

  /**
   * Simulate processing audio through the voice modulation pipeline
   */
  async processAudioChunk(audioBuffer: Buffer, fromUserId: string): Promise<{
    processedAudio: Buffer;
    metrics: CallMetrics;
  }> {
    if (!this.activeCall) {
      throw new Error('No active call');
    }

    const startTime = Date.now();
    
    // Find the participant
    const participant = this.activeCall.participants.find(p => p.userId === fromUserId);
    if (!participant) {
      throw new Error('Participant not found in call');
    }

    try {
      // Simulate voice modulation processing
      const processedAudio = await this.voiceModulation.modulateVoice(
        audioBuffer,
        participant.voiceProfile,
        fromUserId
      );

      const processingTime = Date.now() - startTime;
      
      // Update metrics
      this.activeCall.metrics.processingTime = processingTime;
      this.activeCall.metrics.totalLatency = processingTime + participant.latency;
      
      // Simulate some quality degradation over time
      if (processingTime > 100) {
        this.activeCall.metrics.audioQuality = Math.max(70, this.activeCall.metrics.audioQuality - 1);
      }

      this.emit('audioProcessed', {
        callId: this.activeCall.callId,
        fromUserId,
        processingTime,
        totalLatency: this.activeCall.metrics.totalLatency
      });

      return {
        processedAudio,
        metrics: { ...this.activeCall.metrics }
      };

    } catch (error) {
      console.error('Audio processing failed:', error);
      this.activeCall.metrics.packetsLost++;
      this.activeCall.metrics.audioQuality = Math.max(50, this.activeCall.metrics.audioQuality - 5);
      
      throw error;
    }
  }

  /**
   * Get current call metrics
   */
  getCallMetrics(): CallMetrics | null {
    return this.activeCall ? { ...this.activeCall.metrics } : null;
  }

  /**
   * End the current call
   */
  endCall(): void {
    if (this.activeCall) {
      const duration = Date.now() - this.activeCall.startTime.getTime();
      
      this.emit('callEnded', {
        callId: this.activeCall.callId,
        duration,
        finalMetrics: this.activeCall.metrics
      });

      this.activeCall = null;
    }
  }

  /**
   * Simulate network latency (realistic values for voice calls)
   */
  private simulateNetworkLatency(): number {
    // Simulate realistic network latency: 20-150ms
    return Math.floor(Math.random() * 130) + 20;
  }

  /**
   * Start real-time metrics simulation
   */
  private startMetricsSimulation(): void {
    if (!this.activeCall) return;

    const interval = setInterval(() => {
      if (!this.activeCall) {
        clearInterval(interval);
        return;
      }

      // Simulate network fluctuations
      this.activeCall.participants.forEach(p => {
        p.latency = this.simulateNetworkLatency();
      });

      // Update network latency average
      const avgLatency = this.activeCall.participants.reduce((sum, p) => sum + p.latency, 0) / this.activeCall.participants.length;
      this.activeCall.metrics.networkLatency = avgLatency;

      // Emit metrics update
      this.emit('metricsUpdate', {
        callId: this.activeCall.callId,
        metrics: this.activeCall.metrics
      });

    }, 1000); // Update every second
  }

  /**
   * Simulate different network conditions
   */
  simulateNetworkCondition(condition: 'excellent' | 'good' | 'poor' | 'unstable'): void {
    if (!this.activeCall) return;

    let latencyMultiplier = 1;
    let qualityImpact = 0;

    switch (condition) {
      case 'excellent':
        latencyMultiplier = 0.5;
        qualityImpact = 5;
        break;
      case 'good':
        latencyMultiplier = 1;
        qualityImpact = 0;
        break;
      case 'poor':
        latencyMultiplier = 2;
        qualityImpact = -10;
        break;
      case 'unstable':
        latencyMultiplier = 1.5 + Math.random();
        qualityImpact = -5 - Math.random() * 10;
        break;
    }

    this.activeCall.participants.forEach(p => {
      p.latency = Math.floor(p.latency * latencyMultiplier);
    });

    this.activeCall.metrics.audioQuality = Math.max(30, 
      Math.min(100, this.activeCall.metrics.audioQuality + qualityImpact)
    );

    this.emit('networkConditionChanged', { condition, metrics: this.activeCall.metrics });
  }

  /**
   * Get latency analysis for the current call
   */
  getLatencyAnalysis(): {
    acceptable: boolean;
    recommendation: string;
    breakdown: {
      processing: number;
      network: number;
      total: number;
    };
  } | null {
    if (!this.activeCall) return null;

    const metrics = this.activeCall.metrics;
    const total = metrics.totalLatency;
    
    return {
      acceptable: total < 150, // Under 150ms is generally acceptable for voice calls
      recommendation: total < 100 
        ? 'Excellent call quality' 
        : total < 150 
        ? 'Good call quality' 
        : total < 250 
        ? 'Noticeable delay, consider optimizing' 
        : 'Poor call quality, significant delay',
      breakdown: {
        processing: metrics.processingTime,
        network: metrics.networkLatency,
        total: total
      }
    };
  }
}

export default VoiceCallSimulation;
