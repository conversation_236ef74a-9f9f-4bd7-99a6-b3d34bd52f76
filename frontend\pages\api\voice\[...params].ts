// Next.js API route to proxy voice API requests to backend
import type { NextApiRequest, NextApiResponse } from 'next';
import axios from 'axios';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:3000';
  
  // Reconstruct the full path from params
  const { params } = req.query;
  const pathSegments = Array.isArray(params) ? params : [params];
  const fullPath = pathSegments.join('/');
  
  const url = `${backendUrl}/api/voice/${fullPath}`;

  try {
    const response = await axios({
      method: req.method,
      url,
      headers: {
        ...req.headers,
        host: undefined, // Remove host header for backend
      },
      data: req.body,
      params: req.query.params ? undefined : req.query, // Avoid double params
      validateStatus: () => true,
      responseType: req.method === 'POST' && fullPath.includes('test-modulation') ? 'stream' : 'json',
    });
    
    if (req.method === 'POST' && fullPath.includes('test-modulation')) {
      // Handle audio file response
      res.setHeader('Content-Type', response.headers['content-type'] || 'audio/wav');
      response.data.pipe(res);
    } else {
      res.status(response.status).json(response.data);
    }
  } catch (error: any) {
    console.error('Voice API proxy error:', error.message);
    res.status(500).json({ 
      error: 'Proxy error', 
      details: error.message,
      url: url.replace(process.env.NEXT_PUBLIC_BACKEND_URL || '', '[BACKEND_URL]')
    });
  }
}
