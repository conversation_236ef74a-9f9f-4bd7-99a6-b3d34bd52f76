import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import withAdminAuth from '../../components/hoc/withAdminAuth';
import AdminLayout from '../../components/admin/AdminLayout';
import Button from '../../components/admin/Button';
import Icon from '../../components/admin/Icon';
import { apiClient } from '../../utils/axiosClient';

interface VoiceProfile {
  pitch: number;
  tempo: number;
  reverb: number;
  distortion: number;
  formant: number;
  chorus: boolean;
  normalize: boolean;
  description?: string;
  userType?: string;
  sampleUrl?: string;
  isStandard?: boolean;
  isCustom?: boolean;
}

interface CustomVoiceProfile extends VoiceProfile {
  name: string;
  isCustom: true;
  customSoxArgs: string[];
}

interface ServiceStatus {
  soxAvailable: boolean;
  status: string;
  message: string;
  profiles: string[];
  profilesWithSamples: boolean;
  customProfilesSupported: boolean;
  features: string[];
}

interface EnhancedProfilesResponse {
  standardProfiles: VoiceProfile[];
  customExamples: CustomVoiceProfile[];
  totalProfiles: number;
}



interface UserVoiceSettings {
  userId: string;
  username: string;
  defaultMorphingProfile: string;
  voiceCallsEnabled: boolean;
  recordingEnabled: boolean;
}

const VoiceModulationPage: React.FC = () => {
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [selectedProfile, setSelectedProfile] = useState<string>('SECURE_MALE');
  const [serviceStatus, setServiceStatus] = useState<ServiceStatus | null>(null);
  const [availableProfiles, setAvailableProfiles] = useState<Record<string, VoiceProfile>>({});
  const [enhancedProfiles, setEnhancedProfiles] = useState<EnhancedProfilesResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // User voice settings management
  const [userVoiceSettings, setUserVoiceSettings] = useState<UserVoiceSettings[]>([]);
  const [activeTab, setActiveTab] = useState<'testing' | 'settings' | 'custom'>('testing');

  // Custom profile creation
  const [customProfileName, setCustomProfileName] = useState('');
  const [customSoxArgs, setCustomSoxArgs] = useState('');
  const [customDescription, setCustomDescription] = useState('');
  const [isCreatingCustom, setIsCreatingCustom] = useState(false);

  useEffect(() => {
    fetchServiceStatus();
    fetchAvailableProfiles();
    fetchUserVoiceSettings();
    fetchEnhancedProfiles();
  }, []);
  const fetchServiceStatus = async () => {
    try {
      setIsLoading(true);
      const response = await apiClient.backend.get('/api/voice/service-status');
      if (response.data.success) {
        setServiceStatus(response.data.data);
        if (!response.data.data.soxAvailable) {
          setError('Voice modulation service is not available. SoX is not installed on the server.');
        }
      }
    } catch (error) {
      console.error('Failed to fetch service status:', error);
      setError('Failed to check voice modulation service status.');
    } finally {
      setIsLoading(false);
    }
  };
  const fetchAvailableProfiles = async () => {
    try {
      const response = await apiClient.backend.get('/api/voice/profiles');
      if (response.data.success) {
        setAvailableProfiles(response.data.data.profiles);
      }
    } catch (error) {
      console.error('Failed to fetch voice profiles:', error);
    }
  };





  // Fetch user voice settings
  const fetchUserVoiceSettings = async () => {
    try {
      const response = await apiClient.backend.get('/api/voice/user-settings');
      setUserVoiceSettings(response.data.settings || []);
    } catch (error: any) {
      console.error('Failed to fetch user voice settings:', error);
    }
  };

  // Update user default voice profile
  const updateUserVoiceProfile = async (userId: string, profileName: string) => {
    try {
      await apiClient.backend.post('/api/voice/update-user-profile', {
        userId,
        defaultMorphingProfile: profileName
      });
      setSuccess(`Updated default voice profile for user`);
      fetchUserVoiceSettings();
    } catch (error: any) {
      setError(error.response?.data?.error || 'Failed to update user voice profile');
    }
  };

  // Fetch enhanced profiles with samples
  const fetchEnhancedProfiles = async () => {
    try {
      const response = await apiClient.backend.get('/api/voice/profiles-enhanced');
      setEnhancedProfiles(response.data.data);
    } catch (error: any) {
      console.error('Failed to fetch enhanced profiles:', error);
    }
  };

  // Create custom voice profile
  const createCustomProfile = async () => {
    if (!customProfileName || !customSoxArgs) {
      setError('Profile name and SoX arguments are required');
      return;
    }

    setIsCreatingCustom(true);
    setError(null);
    setSuccess(null);

    try {
      const soxArgsArray = customSoxArgs.split(' ').filter(arg => arg.trim());

      const response = await apiClient.backend.post('/api/voice/custom-profile', {
        name: customProfileName,
        customSoxArgs: soxArgsArray,
        description: customDescription || undefined
      });

      setSuccess(`Custom profile "${customProfileName}" created successfully!`);
      setCustomProfileName('');
      setCustomSoxArgs('');
      setCustomDescription('');
      fetchEnhancedProfiles();

    } catch (error: any) {
      setError(error.response?.data?.error || 'Failed to create custom profile');
    } finally {
      setIsCreatingCustom(false);
    }
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <Head>
          <title>Voice Modulation | CCALC Admin Panel</title>
        </Head>
        <div className="container">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <Icon name="refresh" className="animate-spin text-4xl mb-4 mx-auto" />
              <p>Loading voice modulation service...</p>
            </div>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <Head>
        <title>Voice Modulation | CCALC Admin Panel</title>
      </Head>
      <div className="container mx-auto px-6 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Voice Modulation System</h1>
          <p className="text-gray-600">
            Advanced voice morphing using SoX (Sound eXchange) for secure, non-reversible voice modulation.
          </p>
        </div>

        {/* Only show critical errors, hide service status clutter */}
        {error && error.includes('Failed') && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center text-red-800">
              <Icon name="warning" className="mr-3" />
              <span>Voice modulation service temporarily unavailable</span>
            </div>
          </div>
        )}

        {success && (
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center text-green-800">
              <Icon name="check" className="mr-3" />
              <span>{success}</span>
            </div>
          </div>
        )}

        {/* Tab Navigation */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {[
                { id: 'testing', label: 'Voice Testing', icon: 'check' },
                { id: 'settings', label: 'User Settings', icon: 'user' },
                { id: 'custom', label: 'Custom Profiles', icon: 'plus' }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon name={tab.icon} className="mr-2" />
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'testing' && (
          <div className="bg-white shadow-lg rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-6">Voice Modulation Testing</h2>

          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Voice Modulation Profile:
            </label>
            <select
              value={selectedProfile}
              onChange={(e) => setSelectedProfile(e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={false}
            >
              {Object.keys(availableProfiles).map((profileName) => (
                <option key={profileName} value={profileName}>
                  {profileName.replace(/_/g, ' ')}
                </option>
              ))}
            </select>
          </div>

          {/* Voice Sample Player - Simplified */}
          {availableProfiles[selectedProfile] && (
            <div className="bg-gray-50 rounded-lg p-4 mb-6">
              <h3 className="text-lg font-semibold mb-3">Preview: {selectedProfile.replace(/_/g, ' ')}</h3>
              <div className="flex items-center space-x-4">
                <audio
                  controls
                  className="flex-1"
                  preload="none"
                  key={selectedProfile}
                >
                  <source
                    src={`/api/voice/profile-sample/${selectedProfile}`}
                    type="audio/wav"
                  />
                  Your browser does not support the audio element.
                </audio>
              </div>
            </div>
          )}

          {/* Voice Testing Section */}
          <div className="bg-white border rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-4">Voice Modulation Testing</h3>

            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                  🎤 Start Recording
                </button>
                <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700">
                  ▶️ Apply Modulation
                </button>
                <button className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700">
                  🔄 Refresh
                </button>
              </div>
            </div>
          </div>
          </div>
        )}

        {/* User Settings Tab */}
        {activeTab === 'settings' && (
          <div className="bg-white shadow-lg rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-6">User Voice Settings</h2>

            <div className="space-y-4">
              {userVoiceSettings.map((user) => (
                <div key={user.userId} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium">{user.username}</h3>
                      <p className="text-sm text-gray-600">
                        Current Profile: {user.defaultMorphingProfile}
                      </p>
                    </div>
                    <div className="flex items-center space-x-4">
                      <select
                        value={user.defaultMorphingProfile}
                        onChange={(e) => updateUserVoiceProfile(user.userId, e.target.value)}
                        className="border border-gray-300 rounded-md px-3 py-2"
                      >
                        {serviceStatus?.profiles.map((profile) => (
                          <option key={profile} value={profile}>
                            {profile.replace('_', ' ')}
                          </option>
                        ))}
                      </select>
                      <div className="flex items-center space-x-2">
                        <span className={`px-2 py-1 rounded text-xs ${
                          user.voiceCallsEnabled
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {user.voiceCallsEnabled ? 'Enabled' : 'Disabled'}
                        </span>
                        <span className={`px-2 py-1 rounded text-xs ${
                          user.recordingEnabled
                            ? 'bg-blue-100 text-blue-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          Recording: {user.recordingEnabled ? 'On' : 'Off'}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Custom Profiles Tab */}
        {activeTab === 'custom' && (
          <div className="bg-white shadow-lg rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-6">Custom Voice Profiles</h2>

            {/* Create Custom Profile */}
            <div className="mb-8 p-6 bg-gray-50 rounded-lg">
              <h3 className="text-lg font-semibold mb-4">Create Custom Profile</h3>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Profile Name
                  </label>
                  <input
                    type="text"
                    value={customProfileName}
                    onChange={(e) => setCustomProfileName(e.target.value)}
                    placeholder="e.g., Deep Echo"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description (Optional)
                  </label>
                  <input
                    type="text"
                    value={customDescription}
                    onChange={(e) => setCustomDescription(e.target.value)}
                    placeholder="e.g., Deep voice with echo effect"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Voice Effects
                </label>
                <textarea
                  value={customSoxArgs}
                  onChange={(e) => setCustomSoxArgs(e.target.value)}
                  placeholder="pitch -5 tempo 0.9 reverb 20"
                  rows={2}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div className="mt-6">
                <Button
                  onClick={createCustomProfile}
                  disabled={!customProfileName || !customSoxArgs || isCreatingCustom}
                  variant="primary"
                >
                  {isCreatingCustom ? (
                    <>
                      <Icon name="refresh" className="animate-spin mr-2" />
                      Creating Profile...
                    </>
                  ) : (
                    <>
                      <Icon name="plus" className="mr-2" />
                      Create Custom Profile
                    </>
                  )}
                </Button>
              </div>
            </div>

            {/* Enhanced Profiles Display */}
            {enhancedProfiles && (
              <div className="space-y-6">
                {/* Standard Profiles */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">Standard Voice Profiles</h3>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    {enhancedProfiles.standardProfiles.map((profile) => (
                      <div key={profile.name} className="border rounded-lg p-4 bg-white">
                        <div className="flex items-center justify-between mb-3">
                          <h4 className="font-medium text-gray-900">
                            {profile.name?.replace(/_/g, ' ')}
                          </h4>
                          <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs">
                            Standard
                          </span>
                        </div>

                        {profile.description && (
                          <p className="text-sm text-gray-600 mb-3">{profile.description}</p>
                        )}



                        {profile.sampleUrl && serviceStatus?.profilesWithSamples && (
                          <div className="mt-3">
                            <audio controls className="w-full h-8">
                              <source src={profile.sampleUrl} type="audio/wav" />
                              Your browser does not support audio.
                            </audio>
                          </div>
                        )}

                        {profile.userType === "users_only" && (
                          <div className="mt-2 text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded">
                            ⚠️ Regular users only (not available to superuser)
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Custom Profile Examples */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">Custom Profile Examples</h3>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    {enhancedProfiles.customExamples.map((profile) => (
                      <div key={profile.name} className="border rounded-lg p-4 bg-gray-50">
                        <div className="flex items-center justify-between mb-3">
                          <h4 className="font-medium text-gray-900">{profile.name}</h4>
                          <span className="px-2 py-1 bg-purple-100 text-purple-700 rounded text-xs">
                            Custom
                          </span>
                        </div>

                        {profile.description && (
                          <p className="text-sm text-gray-600 mb-3">{profile.description}</p>
                        )}


                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

      </div>
    </AdminLayout>
  );
};

export default withAdminAuth(VoiceModulationPage);
